#ifndef LIBARY_VERSION_H
#define LIBARY_VERSION_H

// 只在Linux/Mac平台编译此文件
#if defined(__GNUC__) || defined(__APPLE__)

// 输出so文件version信息
// by zhangkaiqin 2024 5 9
// 对外接口 GetVersionInfo 运行功能 PrintVersionInfo 输出信息
// 调用组件中协定加入固定version信息 如
// const char* lib_info = "RegexRuleEngine build version 3.52.240507 on 2024-05-07 16:50";
// 核心功能调用popen创建管道子进程打印version信息相关数据

#include <stdio.h>
#include <string.h>
#include <vector>
#include <string>

#ifdef _WIN32
#include <windows.h>
#else
#include <unistd.h>  // for getcwd and Mac system calls
#endif

#ifdef __APPLE__
#include <sys/types.h>
#endif

using namespace std;

class libaryVersionInfo
{
private:
	// 预定义的组件顺序（静态常量，避免重复定义）
	static const vector<string> getOrderedLibs() {
		static const vector<string> orderedLibs = {
			"ldfcr", "DlpPolicyEngine", "KWRuleEngine", "RegexRuleEngine",
			"FilePropEngine", "FileFpRuleEngine", "SVMRuleEngine", "FpDbRuleEngine",
			"TrCadFilter", "TrCompressFilter", "TrOLEFilter", "TrOOXMLFilter",
			"TrPdfFilter", "TrRtfFilter", "TrTextExtractor", "TrTxtFilter",
			"TrArchive", "TrODFFilter", "DlpOCR", "DlpSCR", "TrOCRFilter",
			"DlpULog", "trcrt", "lua", "pcre", "svm", "jieba", "xcrbnfa", "memstream"
		};
		return orderedLibs;
	}



private:
	bool compareByPattern(const string v_charPattern, const string v_charMatch)
	{
		int leng = v_charPattern.size();
		string strSub = v_charMatch.substr(0, leng);
		if (strcmp(v_charPattern.c_str(), strSub.c_str()) == 0)
		{
			return true;
		}
		return false;
	}

	void getSensitiveSoVersion(const char * v_char)
	{
		vector<string> vec;
		string str = v_char;
		string strInstruction;
		string libPath = detectLibraryPath();

#if __linux__
		if (str == "TrArchive") {
			strInstruction = "strings " + libPath + str + ".so | grep \"build version\"";
		}
		else {
			strInstruction = "strings " + libPath + "lib" + str + ".so | grep \"build version\"";
		}
#elif __APPLE__
		if (str == "TrArchive") {
			strInstruction = "strings " + libPath + str + ".dylib | grep \"build version\"";
		}
		else {
			strInstruction = "strings " + libPath + "lib" + str + ".dylib | grep \"build version\"";
		}
#elif _WIN32
		// Windows下直接返回简单的版本信息
		string dllPath = libPath + str + ".dll";
		string versionInfo = str + " build version Unknown on Unknown";
		vec.push_back(versionInfo);
		m_vec.push_back(versionInfo);
		return;
#endif

		FILE *fp;

#ifdef _WIN32
		fp = _popen(strInstruction.c_str(), "r");
#else
		fp = popen(strInstruction.c_str(), "r");
#endif

		if (fp == NULL) {
			printf("Failed to run\n");
			return;
		}

		char buffer[1024];
		while (fgets(buffer, sizeof(buffer), fp) != NULL) {
			vec.push_back(buffer);
		}

#ifdef _WIN32
		_pclose(fp);
#else
		pclose(fp);
#endif

		bool matchRes = false;
		for (int i = 0; i < vec.size(); ++i)
		{
			bool res = compareByPattern(v_char, vec[i]);
			if (res)
			{
				m_vec.push_back(vec[i]);
				matchRes = true;
				break;
			}
		}
		if (!matchRes)
		{
			string strError = str + " no version number has been established \n";
			m_vec.push_back(strError);
		}
		return;
	}

	vector<string> m_vec;
	vector<string> m_libPaths;
	vector<string> m_libVec;

public:
	libaryVersionInfo(/* args */);
	~libaryVersionInfo();

	void GetVersionInfo()
	{
		m_vec.clear();
		for (int i = 0; i < m_libVec.size(); ++i)
		{
			getSensitiveSoVersion(m_libVec[i].c_str());
		}
	}

	void GetVersionInfoByOrder();

	void PrintVersionInfo()
	{
		for (int i = 0; i < m_vec.size(); ++i)
		{
			printf("%d. %s", i + 1, m_vec[i].c_str());

			if (i < m_libPaths.size()) {
				string fullPath = m_libPaths[i];

				// 如果是相对路径，转换为绝对路径
#ifdef _WIN32
				if (fullPath.length() < 2 || fullPath[1] != ':') {
					char currentDir[MAX_PATH];
					if (GetCurrentDirectoryA(MAX_PATH, currentDir) != 0) {
						fullPath = string(currentDir) + "\\" + fullPath;
					}
				}
#else
				if (fullPath[0] != '/') {
					char currentDir[1024];
					if (getcwd(currentDir, sizeof(currentDir)) != NULL) {
						fullPath = string(currentDir) + "/" + fullPath;
					}
				}
#endif

				printf("   Location: %s\n", fullPath.c_str());
			}
		}
	}

private:
	// 跨平台库路径检测
	string detectLibraryPath()
	{
		vector<string> possiblePaths = {
#ifdef _WIN32
			"",  // 当前目录（同级目录）
			"libs_x64\\",
			"lib\\dlpcomm\\"
#else
			"libs_x64/",
			"lib/dlpcomm/",
			"../libs_x64/",
			"../lib/dlpcomm/"
#endif
		};

		for (const auto& path : possiblePaths) {
#if __linux__
			string testFile = path + "libtrcrt.so";
			FILE* file = fopen(testFile.c_str(), "r");
			if (file != NULL) {
				fclose(file);
				return path;
			}

			testFile = path + "TrArchive.so";
			FILE* file2 = fopen(testFile.c_str(), "r");
			if (file2 != NULL) {
				fclose(file2);
				return path;
			}
#elif __APPLE__
			string testFile = path + "libtrcrt.dylib";
			FILE* file = fopen(testFile.c_str(), "r");
			if (file != NULL) {
				fclose(file);
				return path;
			}

			testFile = path + "TrArchive.dylib";
			FILE* file2 = fopen(testFile.c_str(), "r");
			if (file2 != NULL) {
				fclose(file2);
				return path;
			}
	#elif _WIN32
			string testFile = path + "trcrt.dll";
			FILE* file = fopen(testFile.c_str(), "r");
			if (file != NULL) {
				fclose(file);
				return path;
			}

			testFile = path + "ldfcr.dll";
			FILE* file2 = fopen(testFile.c_str(), "r");
			if (file2 != NULL) {
				fclose(file2);
				return path;
			}
#endif
		}

		printf("Warning: No valid library path found, using default lib/dlpcomm/\n");
		return "lib/dlpcomm/";
	}

	void extractLibNameFromPath(const string& fullPath, vector<string>& libNames);

	bool isTargetLibrary(const string& libName, const string& fullPath);

#ifdef __APPLE__
	// 临时容器
	void getRealLoadedLibrariesMacToTemp(vector<string>& libNames, vector<string>& libPaths);
#endif

#if __linux__
	// 临时容器
	void getRealLoadedLibrariesLinuxToTemp(vector<string>& libNames, vector<string>& libPaths);
#endif

#ifdef _WIN32
		// Windows平台轻量级实现
		void getRealLoadedLibrariesWindowsToTemp(vector<string>& libNames, vector<string>& libPaths);
#endif
};

libaryVersionInfo::libaryVersionInfo()
{
	m_vec.clear();
	m_libPaths.clear();
	m_libVec = getOrderedLibs(); // 直接使用静态函数获取有序列表
}

libaryVersionInfo::~libaryVersionInfo()
{
}

// 按指定顺序获取实际加载的库信息
void libaryVersionInfo::GetVersionInfoByOrder()
{
	m_vec.clear();
	m_libPaths.clear();

	const vector<string>& orderedLibs = getOrderedLibs();

	vector<string> detectedLibs;
	vector<string> detectedPaths;

#ifdef __APPLE__
	getRealLoadedLibrariesMacToTemp(detectedLibs, detectedPaths);
#elif __linux__
	getRealLoadedLibrariesLinuxToTemp(detectedLibs, detectedPaths);
#elif _WIN32
	getRealLoadedLibrariesWindowsToTemp(detectedLibs, detectedPaths);
#endif

	for (const auto& libName : orderedLibs) {
		for (size_t i = 0; i < detectedLibs.size(); ++i) {
			if (detectedLibs[i] == libName) {
				getSensitiveSoVersion(libName.c_str());
				m_libPaths.push_back(detectedPaths[i]);
				break;
			}
		}
	}

	if (m_vec.empty()) {
		printf("Warning: No loaded libraries detected, using traditional method\n");
		string libPath = detectLibraryPath();

		for (const auto& libName : orderedLibs) {
			string fullPath;
#if __linux__
			if (libName == "TrArchive") {
				fullPath = libPath + libName + ".so";
			}
			else {
				fullPath = libPath + "lib" + libName + ".so";
			}
#elif __APPLE__
			if (libName == "TrArchive") {
				fullPath = libPath + libName + ".dylib";
			}
			else {
				fullPath = libPath + "lib" + libName + ".dylib";
			}
#elif _WIN32
			fullPath = libPath + libName + ".dll";
#endif

			FILE* file = fopen(fullPath.c_str(), "r");
			if (file != NULL) {
				fclose(file);
				getSensitiveSoVersion(libName.c_str());
				m_libPaths.push_back(fullPath);
			}
		}
	}
}



// 统一的路径提取函数，支持跨平台
void libaryVersionInfo::extractLibNameFromPath(const string& fullPath, vector<string>& libNames)
{
	size_t lastSlash = fullPath.find_last_of('/');
	if (lastSlash == std::string::npos) return;

	std::string fileName = fullPath.substr(lastSlash + 1);

#if __linux__
	size_t versionPos = fileName.find(".so.");
	if (versionPos != std::string::npos) {
		fileName = fileName.substr(0, versionPos + 3);
	}
	if (fileName.length() > 3 && fileName.substr(fileName.length() - 3) == ".so") {
		fileName = fileName.substr(0, fileName.length() - 3);
	}
#elif __APPLE__
	size_t dylibPos = fileName.find(".dylib");
	if (dylibPos != std::string::npos) {
		fileName = fileName.substr(0, dylibPos);
	}
#endif

	if (fileName.length() > 3 && fileName.substr(0, 3) == "lib") {
		fileName = fileName.substr(3);
	}

	if (!fileName.empty()) {
		libNames.push_back(fileName);
	}
}

// 检查是否为目标库
bool libaryVersionInfo::isTargetLibrary(const string& libName, const string& fullPath)
{
	for (const auto& targetLib : m_libVec) {
		if (libName == targetLib) {
			return true;
		}
	}
	return false;
}



#ifdef __APPLE__
// Mac系统获取实际加载的动态库到临时容器
void libaryVersionInfo::getRealLoadedLibrariesMacToTemp(vector<string>& libNames, vector<string>& libPaths)
{
	// Mac系统使用lsof命令获取加载的动态库，比vmmap更可靠
	string cmd = "lsof -p " + to_string(getpid()) + " 2>/dev/null | grep '\\.dylib' | awk '{for(i=9;i<=NF;i++) printf \"%s \", $i; print \"\"}' | sed 's/ $//' | sort | uniq";

	FILE* pipe = popen(cmd.c_str(), "r");
	if (pipe == NULL) {
		printf("Warning: Failed to execute lsof command on Mac\n");
		return;
	}

	char line[1024];
	while (fgets(line, sizeof(line), pipe) != NULL) {
		string fullPath = string(line);
		if (!fullPath.empty() && fullPath.back() == '\n') {
			fullPath.pop_back();
		}

		if (!fullPath.empty()) {
			std::vector<std::string> extractedLibNames;
			extractLibNameFromPath(fullPath, extractedLibNames);

			for (const auto& libName : extractedLibNames) {
				if (isTargetLibrary(libName, fullPath)) {
					libNames.push_back(libName);
					libPaths.push_back(fullPath);
				}
			}
		}
	}

	pclose(pipe);
}
#endif

#if __linux__
// Linux系统获取实际加载的动态库到临时容器
void libaryVersionInfo::getRealLoadedLibrariesLinuxToTemp(vector<string>& libNames, vector<string>& libPaths)
{
	FILE* file = fopen("/proc/self/maps", "r");
	if (file == NULL) {
		printf("Warning: Failed to open /proc/self/maps on Linux\n");
		return;
	}

	char line[1024];
	while (fgets(line, sizeof(line), file) != NULL) {
		string mapLine = string(line);

		if (mapLine.find(".so") != string::npos) {
			size_t lastSpace = mapLine.find_last_of(' ');
			if (lastSpace != string::npos) {
				string fullPath = mapLine.substr(lastSpace + 1);
				if (!fullPath.empty() && fullPath.back() == '\n') {
					fullPath.pop_back();
				}

				if (!fullPath.empty()) {
					std::vector<std::string> extractedLibNames;
					extractLibNameFromPath(fullPath, extractedLibNames);

					for (const auto& libName : extractedLibNames) {
						if (isTargetLibrary(libName, fullPath)) {
							bool exists = false;
							for (const auto& existingLib : libNames) {
								if (existingLib == libName) {
									exists = true;
									break;
								}
							}
							if (!exists) {
								libNames.push_back(libName);
								libPaths.push_back(fullPath);
							}
						}
					}
				}
			}
		}
	}

	fclose(file);
}
#endif

#ifdef _WIN32
// Windows系统轻量级获取DLL信息 - 扫描预定义路径
void libaryVersionInfo::getRealLoadedLibrariesWindowsToTemp(vector<string>& libNames, vector<string>& libPaths)
{
	// 获取当前程序目录
	char currentDir[MAX_PATH];
	GetCurrentDirectoryA(MAX_PATH, currentDir);
	string baseDir = string(currentDir);

	// 预定义可能的DLL路径（优先检查同级目录）
	vector<string> searchPaths = {
		baseDir,  // 当前目录（同级目录）
		baseDir + "\\libs_x64",
		baseDir + "\\lib\\dlpcomm"
	};

	const vector<string>& orderedLibs = getOrderedLibs();

	for (const auto& libName : orderedLibs) {
		for (const auto& searchPath : searchPaths) {
			string dllPath;
			if (searchPath.empty()) {
				dllPath = baseDir + "\\" + libName + ".dll";
			} else {
				dllPath = searchPath + "\\" + libName + ".dll";
			}

			// 检查文件是否存在
			WIN32_FIND_DATAA findData;
			HANDLE hFind = FindFirstFileA(dllPath.c_str(), &findData);
			if (hFind != INVALID_HANDLE_VALUE) {
				FindClose(hFind);
				libNames.push_back(libName);
				libPaths.push_back(dllPath);
				break; // 找到就跳出内层循环
			}
		}
	}
}
#endif

#endif // defined(__GNUC__) || defined(__APPLE__)

#endif // LIBARY_VERSION_H