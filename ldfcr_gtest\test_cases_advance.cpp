#include <vector>
#include <string>
#ifdef __GNUC__
#include <dirent.h>
#endif
#ifdef WIN32
#include <strsafe.h>
//#include <vld.h>
#endif

#include <future>

#include "test_class.h"

#include "rapidjson/rapidjson.h"   //引入json串解析的能力
#include "rapidjson/document.h"
#include "trcrt.h"
#include "DlpULog.h"
//#include "utils/JsonValueAdapter.h"

extern int g_log_handle;

using namespace std;

#define NOT_FIRSTCHAR(c) (((c) & 0xc0) == 0x80)

#define FIRSTCHAR(c) !NOT_FIRSTCHAR(c)

// 是否打开多线程测试用例
#define MULTITHREAD 1

//#define _CRT_SECURE_NO_WARNINGS

int iSkipTestNum = 0;//跳过的测试用例数

#if MULTITHREAD == 1
// valgrind 非常耗性能
#ifndef VALGRIND_CHECK
const int loop = 10000;
#else
const int loop = 100;
#endif
#endif

//#define CLEAN_UP(reg, drip)	\
//    if (reg) {				\
//        reg->Release();		\
//        reg = NULL;			\
//    }						\
//    if (drip) {				\
//        drip->Release();	\
//        drip = NULL;		\
//    }
//
//
//// 计数字节的开头位为1的数目
//// count first 6 bits
//static int NumLeadingBitsSet_bak(char c)
//{
//    int i = 7, count = 0;
//    while (i > 1 && c&(1<<i))
//    {
//        ++count;
//        --i;
//    }
//    return count;
//}
//
//// 判断字节是否是UTF-8字符的结尾字节
//static bool IsUTF8EndByte_bak(const char *start, const char *p)
//{
//    if (isascii(*p))
//        return true;
//
//    // begining byte 0x40 == 1 << 6, 左起第二位是否为1
//    if ((*p&0x40) == 0x40)
//        return false;
//
//    int count = 0;
//    // 0xBF = 10XXXXXX
//    while (p >= start && (*p&0xBF) == (unsigned char)*p)
//    {
//        ++count;
//        --p;
//    }
//    if (!count)
//        return false;
//
//    return count+1 == NumLeadingBitsSet(*p);
//}
//
//#ifdef __GNUC__
//// 读取目录下的文件
//vector<string> read_dir(const char *dir)
//{
//    vector<string> fvec;
//    DIR *dh = opendir(dir);
//    if (dh == NULL) {
//        return fvec;
//    }
//
//    string path = dir;
//    if (path[path.size()-1] != '/')
//        path += "/";
//    struct dirent *dirp;
//    while ((dirp = readdir(dh)) != NULL) {
//        if (dirp->d_type & DT_REG) {
//            fvec.push_back(path+dirp->d_name);
//        }
//    }
//    closedir(dh);
//    return fvec;
//}
//#endif
//
//#ifdef WIN32
//
//vector<wstring> read_dir_bak(const TCHAR *dir)
//{
//    TCHAR szDir[MAX_PATH];
//    WIN32_FIND_DATA ffd;
//    LARGE_INTEGER filesize;
//    DWORD dwError = 0;
//    HANDLE hFind = INVALID_HANDLE_VALUE;
//    vector<wstring> fvec;
//
//    WCHAR wcsAppDir[MAX_PATH];
//    ::GetModuleFileNameW(NULL, wcsAppDir, sizeof(wcsAppDir) / sizeof(WCHAR));
//    int  iLen = ::wcslen(wcsAppDir);
//    while (0 < iLen)
//    {
//        if (L'\\' == wcsAppDir[iLen - 1]) {
//            wcsAppDir[iLen - 1] = 0;
//            break;
//        }
//        iLen--;
//    }
//
//    std::wstring m_strStrategyFilePath = wcsAppDir;    //构造策略文件的路径
//    m_strStrategyFilePath += L'\\';
//    m_strStrategyFilePath += dir;
//
//    for (int i = 0; i < m_strStrategyFilePath.size(); ++i)
//    {
//        if (m_strStrategyFilePath[i] == L'/')
//            m_strStrategyFilePath[i] = L'\\';
//    }
//    if (m_strStrategyFilePath[m_strStrategyFilePath.size() - 1] != L'\\')
//    {
//        m_strStrategyFilePath += L'\\';
//    }
//
//    // wprintf(TEXT("open dir %s  w\n"), m_strStrategyFilePath.c_str());
//
//    StringCchCopy(szDir, MAX_PATH, m_strStrategyFilePath.c_str());
//    StringCchCat(szDir, MAX_PATH, TEXT("*"));
//
//    hFind = FindFirstFile(szDir, &ffd);
//    if (INVALID_HANDLE_VALUE == hFind)
//    {
//        return vector<wstring>();
//    }
//    do
//    {
//        if (ffd.dwFileAttributes & FILE_ATTRIBUTE_DIRECTORY)
//        {
//            // wprintf(TEXT("  %s   <DIR>\n"), ffd.cFileName);
//        }
//        else
//        {
//            filesize.LowPart = ffd.nFileSizeLow;
//            filesize.HighPart = ffd.nFileSizeHigh;
//            // wprintf(TEXT("  %s   %ld bytes\n"), ffd.cFileName, filesize.QuadPart);
//
//            TCHAR szFullPath[MAX_PATH];
//            StringCchCopy(szFullPath, MAX_PATH, m_strStrategyFilePath.c_str());
//            //StringCchCat(szFullPath, MAX_PATH, TEXT("\\"));
//            StringCchCat(szFullPath, MAX_PATH, ffd.cFileName);
//            fvec.push_back(szFullPath);
//        }
//    } while (FindNextFile(hFind, &ffd) != 0);
//
//    FindClose(hFind);
//    return fvec;
//}
//
//// 获取文件内容
//static char* getFileContent_bak(const WCHAR *filePath, long *psize)
//{
//    if (filePath == NULL)
//        return NULL;
//    std::wstring m_strStrategyFilePath;
//    WCHAR c = tolower(filePath[0]);
//    if (c >= L'a' && c <= L'z' && filePath[1] == L':')
//    {
//    }
//    else
//    {
//        WCHAR wcsAppDir[MAX_PATH];
//        ::GetModuleFileNameW(NULL, wcsAppDir, sizeof(wcsAppDir) / sizeof(WCHAR));
//        int  iLen = ::wcslen(wcsAppDir);
//        while (0 < iLen)
//        {
//            if (L'\\' == wcsAppDir[iLen - 1]) {
//                wcsAppDir[iLen - 1] = 0;
//                break;
//            }
//            iLen--;
//        }
//
//        m_strStrategyFilePath = wcsAppDir;
//        m_strStrategyFilePath += L'\\';
//    }
//
//    m_strStrategyFilePath += filePath;
//
//    size_t i = 0;
//    while (i < m_strStrategyFilePath.size())
//    {
//        if (L'/' == m_strStrategyFilePath[i])
//        {
//            m_strStrategyFilePath[i] = L'\\';
//        }
//        ++i;
//    }
//
//     FILE *fp = NULL;
//    _wfopen_s(&fp, m_strStrategyFilePath.c_str(), L"rb");
//    EXPECT_NE(fp, nullptr);
//
//    fseek(fp, 0, SEEK_END);
//    long size = ftell(fp);
//    fseek(fp, 0, SEEK_SET);
//
//    char *stra_content = (char *)calloc(size+1, 1);
//
//    size_t nret = fread(stra_content, 1, size, fp);
//    EXPECT_EQ(nret, (size_t)size);
//    //EXPECT_NE(psize, NULL);
//    *psize = size;
//
//    fclose(fp);
//    return stra_content;
//}
//#else
//// 获取文件内容
//static char* getFileContent_bak(const char *filePath, long *psize)
//{
//    FILE *fp = fopen(filePath, "r");
//    if (!fp)
//        return nullptr;
//
//    fseek(fp, 0, SEEK_END);
//    long size = ftell(fp);
//    fseek(fp, 0, SEEK_SET);
//
//    char *stra_content = (char *)calloc(size+1, 1);
//
//    fread(stra_content, 1, size, fp);
//
//    fclose(fp);
//    return stra_content;
//}
//#endif
//
//void LdfcrSleep_bak(int sec)
//{
//#ifdef __GNUC__
//    sleep(sec);
//#else
//    Sleep(sec*1000);
//#endif
//}
//
//#ifdef WIN32
//wstring str2ws_bak(const char *ws, size_t iLen)
//{
//    if (ws == NULL)
//        return L"";
//    //trcrt::str::SafeString  strategy_sstr;
//    //trcrt::str::tr_str_initString(&strategy_sstr);
//	tr::str::CUnicodeString strategy_sstr;
//	strategy_sstr.convertFromUtf8(ws);
//    //strategy_sstr->set_fromUTF8(
//    //    trcrt::str::IString::UTF16,
//    //    ws, iLen
//    //    );
//    return wstring(strategy_sstr.wstr());
//}
//
//wstring StringToWstring_bak(const string v_str)
//{// string转wstring
//	unsigned len = v_str.size() * 2;// 预留字节数
//	setlocale(LC_CTYPE, "");     //必须调用此函数
//	wchar_t *p = new wchar_t[len];// 申请一段内存存放转换后的字符串
//	mbstowcs(p, v_str.c_str(), len);// 转换
//	wstring str1(p);
//	delete[] p;// 释放申请的内存
//	return str1;
//}
//
//string WstringToString_bak(const wstring v_wstr)
//{// wstring转string
//	unsigned len = v_wstr.size() * 4;
//	setlocale(LC_CTYPE, "");
//	char *p = new char[len];
//	wcstombs(p, v_wstr.c_str(), len);
//	string str1(p);
//	delete[] p;
//	return str1;
//}
//#endif
//
///*
//获取策略完整路径  
//wchar * 改为char* 适配linux 2023 8 15
//*/
//string GetCompletePath_bak(const char* v_subPath)
//{
//#ifdef WIN32
//	wchar_t temp[MAX_PATH] = {};
//	GetModuleFileNameW(GetModuleHandle(NULL), temp, MAX_PATH);
//	std::wstring parentPathW = temp;
//	size_t lastSlashPos = parentPathW.find_last_of(L'\\');
//	if (lastSlashPos != std::wstring::npos)
//	{
//		parentPathW.erase(lastSlashPos); // 删除最后一个文件名及其之后的部分
//	}
//	parentPathW += StringToWstring(v_subPath);
//	return WstringToString(parentPathW);
//#elif __GNUC__
//	const int MAXPATH = 250;
//	char buffer[MAXPATH];
//	getcwd(buffer, MAXPATH);
//	std::string strPath = buffer;
//	//确保路径以斜杠结尾
//	if (strPath.back() != '/')
//	{
//		strPath += '/';
//	}
//	strPath += v_subPath;//拼接子路径
//	return strPath;
//#endif
//}

//判断对应测试用例是否开启自动化测试
BOOL JudgeFileJoinTest(std::string testName)
{
#ifdef WIN32
	char LP[1024];
	string strSrcIni = "./JoinTestFile.ini";
	std::ifstream configFile(strSrcIni);
	std::string strJoinTestFile = GetCompletePath("/JoinTestFile.ini");

	//配置文件存在
	if (configFile.good())
	{
		dlplog_info(g_log_handle, "[%d] %s", __LINE__, strJoinTestFile.c_str());
	}
	else
	{
		dlplog_info(g_log_handle, "[%d] %s %s", __LINE__, "Does not exist:", strJoinTestFile.c_str());
		return TRUE;
	}

	// 构造测试用例名称
	GetPrivateProfileStringA("case", testName.c_str(), "NULL", LP, 512, strSrcIni.c_str());
	int testValue2 = GetPrivateProfileIntA("case", testName.c_str(), 0, strSrcIni.c_str());
	int testValue = atoi(LP);

	//关闭测试
	if (testValue2 == 0)
	{
		iSkipTestNum++;
		std::cout << "this test case has been canceled ......" << endl;
		return FALSE;
	}
	else if (testValue == 0)
	{
		return FALSE;
	}
	//开启测试
	return TRUE;
#else 
	std::string strSrcIni = "./JoinTestFile.ini";
	std::ifstream file(strSrcIni);

	std::string completePath = GetCompletePath("/JoinTestFile.ini");

	//配置文件存在
	if (file.good())
	{
		dlplog_info(g_log_handle, "[%d] %s %s", __LINE__, completePath.c_str(), "JoinTestFile.ini");
	}
	else
	{
		dlplog_info(g_log_handle, "[%d] %s %s", __LINE__, "Does not exist:", completePath.c_str());
		return true;
	}

	std::map<std::string, bool> testMap;
	std::string line;
	while (std::getline(file, line))
	{
		//解析key和value
		std::istringstream iss(line);
		std::string key, value;
		if (std::getline(iss, key, '=') && std::getline(iss, value))
		{
			testMap[key] = (std::stoi(value) == 1);
		}
	}
	auto it = testMap.find(testName);
	if (it != testMap.end())
	{
		if (it->second)
		{
			return true;
		}
		else
		{
			iSkipTestNum++;
			std::cout << "this test case has been canceled ......" << std::endl;
			return false;
		}
	}
	else
	{
		std::cout << "Test case '" << testName << "' is not found in the configuration file." << std::endl;
		return false;
	}
#endif
}

//输出用例测试相关情况
void GetTestinfo()
{
	dlplog_info(g_log_handle, "[%d] %s %d", __LINE__, "The total number of skipped use cases is ", iSkipTestNum);
	printf("\nThe total number of skipped use cases is %d\n", iSkipTestNum);
	std::string strJoinTestFile = GetCompletePath("/JoinTestFile.ini");
	dlplog_info(g_log_handle, "[%d] %s %s", __LINE__, "The path to the JoinTestFile.ini file is in:", strJoinTestFile.c_str());
	printf("The path to the JoinTestFile.ini file is in: %s \n", strJoinTestFile.c_str());
}

#if 1

// Server Advance 
TEST_F(LdfcrFixture, FileFingleTest) {
	string testName = ::testing::UnitTest::GetInstance()->current_test_info()->name();
	if (!JudgeFileJoinTest(testName))
		return;

	EXPECT_EQ(ldfcr.init(_T("test/data/FileFingle.data")), 0);
	IFCRResult *result = NULL;
	IFCRDripResult *dripResult = NULL;
	EXPECT_TRUE(ldfcr.detect(_T("test/FileFingle/harryPotterUnicode.txt"), (void**)&result, (void**)&dripResult));

	CLEAN_UP(result, dripResult);
}

//文件指纹集合
TEST_F(LdfcrFixture, File_FingerPrintGather)
{
	string testName = ::testing::UnitTest::GetInstance()->current_test_info()->name();
	if (!JudgeFileJoinTest(testName))
		return;

	EXPECT_EQ(ldfcr.init(_T("test/data/fileFP_Gather.data")), 0);
	IFCRResult *result = NULL;
	IFCRDripResult *dripResult = NULL;
	EXPECT_TRUE(ldfcr.detect_Dir(_T("test/FileFingerGather"), (void**)&result, (void**)&dripResult));

	CLEAN_UP(result, dripResult);
}

TEST_F(LdfcrFixture, FPDBTest) {
	string testName = ::testing::UnitTest::GetInstance()->current_test_info()->name();
	if (!JudgeFileJoinTest(testName))
		return;

	EXPECT_EQ(ldfcr.init(_T("test/data/FPDBtest.data")), 0);
	IFCRResult *result = NULL;
	IFCRDripResult *dripResult = NULL;
	EXPECT_TRUE(ldfcr.detect(_T("test/FPDB/FPDBSourceTest.txt"), (void**)&result, (void**)&dripResult));
	//EXPECT_TRUE(ldfcr.detect(_T("test/FileFingle/harryPotterUnicode.txt"), nullptr, nullptr));

	CLEAN_UP(result, dripResult);
}

//试用版DLP3.5服务器
TEST_F(LdfcrFixture, FPDB_TryOut)
{
	string testName = ::testing::UnitTest::GetInstance()->current_test_info()->name();
	if (!JudgeFileJoinTest(testName))
		return;

	EXPECT_EQ(ldfcr.init(_T("test/data/FPDB_TryOut.data")), 0);
	IFCRResult *result = NULL;
	IFCRDripResult *dripResult = NULL;
	EXPECT_TRUE(ldfcr.detect(_T("test/FPDB/FPDB_TryOut.txt"), (void**)&result, (void**)&dripResult));

	CLEAN_UP(result, dripResult);
}

TEST_F(LdfcrFixture, SVMFowardTest) {
	string testName = ::testing::UnitTest::GetInstance()->current_test_info()->name();
	if (!JudgeFileJoinTest(testName))
		return;

	EXPECT_EQ(ldfcr.init(_T("test/data/Document_classification.data")), 0);
	IFCRResult *result = NULL;
	IFCRDripResult *dripResult = NULL;
	EXPECT_TRUE(ldfcr.detect_Dir(_T("test/SVMPart/+1"), (void**)&result, (void**)&dripResult));
	CLEAN_UP(result, dripResult);

	result = NULL;
	dripResult = NULL;
	EXPECT_TRUE(ldfcr.detect_Dir(_T("test/SVMPart/-1"), (void**)&result, (void**)&dripResult));
	//EXPECT_TRUE(ldfcr.detect(_T("test/FileFingle/harryPotterUnicode.txt"), nullptr, nullptr));

	CLEAN_UP(result, dripResult);
}

// 三种高级算法：被训练的数据库指纹和文档分类组合成一个文本 拿去训练文件指纹
TEST_F(LdfcrFixture, MergeThreeAdvanceAlog) {
	string testName = ::testing::UnitTest::GetInstance()->current_test_info()->name();
	if (!JudgeFileJoinTest(testName))
		return;

	EXPECT_EQ(ldfcr.init(_T("test/data/threeAdvanceAlog.data")), 0);
	IFCRResult *result = NULL;
	IFCRDripResult *dripResult = NULL;
	EXPECT_TRUE(ldfcr.detect(_T("test/threeAdvanceAlog.txt"), (void**)&result, (void**)&dripResult));

	CLEAN_UP(result, dripResult);
}

// 获取未下载规则文件列表 （请注意：data/rulefiles文件夹中的规则文件如果不在策略中，将被删除）
TEST_F(LdfcrFixture, Get_DBFP_FILEFP_SVM_Regular_FileList)
{
	string testName = ::testing::UnitTest::GetInstance()->current_test_info()->name();
	if (!JudgeFileJoinTest(testName))
		return;

	dlplog_info(g_log_handle, "[%d] ===========================================TEST_F Get_DBFP_FILEFP_SVM_Regular_FileList is started===========================================", __LINE__);
	std::string completePath = GetCompletePath("/test/data/threeAdvanceAlog.data");
	dlplog_info(g_log_handle, "[%d] %s", __LINE__, completePath.c_str());

	if (!operSysJugXP()) // XP版本不支持
	{
		//EXPECT_EQ(ldfcr.insert_key("14fee2b0ffb3836b224721215778d1018a062ce16055d41e247fd87ad0ad021ae1"), 1);
		EXPECT_EQ(ldfcr.init(_T("test/data/threeAdvanceAlog.data")), 0);

		Sleep(10000);
		struct TestData
		{
			std::string md5;
			std::string guid;
			int svrId;
		};
		std::vector<TestData> testDataList =
		{
			{ "10fec4c7442f3478035e38a7a72ef064", "c1d20308-ab11-4aa2-a344-06a88441142d", 2147483647 },
			{ "267e56e12900049f4008c2c349f444bb", "64985c63-23ae-4ced-aa00-b19799315ecc", 2147483647 },
			{ "cb7c4b0bfaaa1c1be7d49530dd956856", "e4d4508d-04b6-4ebc-8381-cfe7c0ab7fa6", 2147483647 }
		};
		// 循环测试每组数据
		for (size_t i = 0; i < testDataList.size(); ++i)
		{
			const auto& testData = testDataList[i];
			bool result = ldfcr.getRuleFilesListForServer(testData.md5, testData.guid, testData.svrId);
			dlplog_info(g_log_handle, "[%d] File %zu: %s", __LINE__, i+1, result ? "PASS" : "FAIL");
			EXPECT_TRUE(result);
		}

	}
}

#endif
