#ifndef TEST_CLASS_H
#define TEST_CLASS_H

#include <stdio.h>
#include <mutex>
#include <future>
#include <cctype>
#include <fstream>
#include <exception>

#include "gtest/gtest.h"
#include "ldfcr_gtest_PubFunc.h"
#include "ldfcr.h"
#include "rapidjson_wrapper_inc.h"
#include "DlpULog.h"

#ifdef WIN32
typedef wchar_t TCHAR, *PTCHAR;
#else
typedef char TCHAR, *PTCHAR;
#endif

#ifndef _T
#ifdef WIN32
#define _T(x)      L ## x
#else
#define _T(x)      x
#endif
#endif
#define MAX_FILE_COUNT          4096

#define CLEAN_COM(reg)		\
    if (reg) {				\
        reg->Release();		\
        reg = NULL;			\
    }						\

#define CLEAN_DRIP(drip)	\
    if (drip) {				\
        drip->Release();	\
        drip = NULL;		\
    }						\

#define CLEAN_UP(reg, drip)	\
		CLEAN_COM(reg);		\
		CLEAN_DRIP(drip);	\

#define NOT_FIRSTCHAR(c) (((c) & 0xc0) == 0x80)
#define FIRSTCHAR(c) !NOT_FIRSTCHAR(c)

extern int g_log_handle;

void GetTestinfo();//对外接口 获取用例测试信息

class Environment : public ::testing::Environment {
public:
	~Environment() override {}

	// Override this to define how to set up the environment.
	void SetUp() override
	{
		BOOL ret = ldfcr_InitStartup();
		if (ret == FALSE)
			abort();
		assert(ldfcr_InitStartup() == TRUE);
	}

	// Override this to define how to tear down the environment.
	void TearDown() override
	{
		ldfcr_StopRelease();
	}
};

class QuickTest : public testing::Test {
protected:
	// Remember that SetUp() is run immediately before a test starts.
	// This is a good place to record the start time.
	void SetUp() override { start_time_ = time(nullptr); }

	// TearDown() is invoked immediately after a test finishes.  Here we
	// check if the test was too slow.
	void TearDown() override {
		// Gets the time when the test finishes
		const time_t end_time = time(nullptr);

		// Asserts that the test took no more than ~5 seconds.  Did you
		// know that you can use assertions in SetUp() and TearDown() as
		// well?
		EXPECT_TRUE(end_time - start_time_ <= 6000) << "The test took too long.";
	}

	// The UTC time (in seconds) when the test starts
	time_t start_time_;
};

class Ldfcr
{
public:
	Ldfcr()
	{
		pILDFcr = NULL;
		m_strategy = NULL;
	}

	~Ldfcr()
	{
		if (m_strategy != NULL)
		{
			delete[] m_strategy;
			m_strategy = NULL;
		}

		finalize();
	}

private:
	ILDFcr*   pILDFcr;
	char* m_strategy;
public:
	/*********************************************************************************************************/
	/********************************************测试用例相关操作*********************************************/
	/*********************************************************************************************************/
#ifdef __GNUC__
	int init(const char *def_test_file)
	{
		std::string _strStrategyFilePath;
		getStringFilePath(def_test_file, _strStrategyFilePath);

		// 打开策略文件
		long size = 0;
		m_strategy = getFileContent(_strStrategyFilePath.c_str(), &size);
		if(m_strategy == NULL)
		{
			printf("m_strategy is empty\n");
			return -1;
		}
			
		// 初始化策略引擎
		if (ldfcr_InitStartup())
		{
			if (pILDFcr == NULL)
			{
				ldfcr_CreateInstance((void **)&pILDFcr);
				if (NULL == pILDFcr)
				{
					printf("pILDFcr is empty\n");
					return -1;
				}
			}
		}
		else
		{
			printf("ldfcr_InitStartup failed\n");
			return -1;
		}
 
		// 设置策略
		BOOL ret = pILDFcr->updateStrategy(m_strategy);
		if (!ret)
		{
			printf("Failed to update strategy\n");
			return -1;
		}
		return 0;
	}

	// 策略更新 全局接口
	int g_init(const char *def_test_file)
	{
		std::string _strStrategyFilePath;
		getStringFilePath(def_test_file, _strStrategyFilePath);

		// 打开策略文件
		long size = 0;
		m_strategy = getFileContent(_strStrategyFilePath.c_str(), &size);
		
		if(m_strategy == NULL)
		{
			printf("m_strategy is empty\n");
			return -1;
		}

		if (!ldfcr_InitStartup())
		{
			return -1;
		}

		//设置策略
		BOOL bRet = ldfcr_UpdateStrategy(m_strategy);
		if (!bRet)
		{
			return -1;
		}

		return 0;
	}
#elif defined(WIN32)
	// 策略更新
	int init(const wchar_t *def_test_file)
	{
		std::wstring m_strStrategyFilePath;
		getWStringFilePath(def_test_file, m_strStrategyFilePath);

		long size = 0;
		m_strategy = getFileContent(m_strStrategyFilePath.c_str(), &size);
		if(m_strategy == NULL)
		{
			printf("m_strategy is empty\n");
			return -1;
		}		

		if (ldfcr_InitStartup())
		{
			if (pILDFcr == NULL)
			{
				ldfcr_CreateInstance((void **)&pILDFcr);
				if (NULL == pILDFcr)
				{
					return -1;
				}
			}
		}
		else
		{
			return -1;
		}

		//设置策略
		BOOL bRet = pILDFcr->updateStrategy(m_strategy);
		if (!bRet)
		{
			return -1;
		}
		return 0;
	}

	// 策略更新 全局接口
	int g_init(const wchar_t *def_test_file)
	{
		std::wstring m_strStrategyFilePath;
		getWStringFilePath(def_test_file, m_strStrategyFilePath);

		//same
		long size = 0;
		m_strategy = getFileContent(m_strStrategyFilePath.c_str(), &size);
		if(m_strategy == NULL)
		{
			printf("m_strategy is empty\n");
			return -1;
		}

		if (!ldfcr_InitStartup())
		{
			return -1;
		}

		//设置策略
		BOOL bRet = ldfcr_UpdateStrategy(m_strategy);
		if (!bRet)
		{
			return -1;
		}
		return 0;
	}

#endif
	enum _enTcrOpen
	{
		CLOSE = 0,
		OPEN = 1
	};

	//通用检测接口
	BOOL detect(const TCHAR *path,void **result_p = NULL, void **dripResult_p = NULL, 
		int num = 0, LPFCRPARAM pfcrParam = nullptr, int v_openFcr = _enTcrOpen::OPEN)
	{
		if (NULL == pILDFcr)
		{
			return FALSE;
		}

		std::string m_strTestFilePath;
		m_strTestFilePath.clear();
		getStringFilePath(path, m_strTestFilePath);

		FCRPARAM fcrParam;
		fcrParam.use_all_rule = TRUE;   //启用所有规则
		fcrParam.target_class_code = 3; //达到或超过代码3，即停止
		fcrParam.output_detail = TRUE;  //要求返回时输出策略的细节信息
		fcrParam.devType = 1;
		fcrParam.opType = num;

		if (pfcrParam == nullptr)
			pfcrParam = &fcrParam;
	
		if (result_p)
			*result_p = nullptr;
		if (dripResult_p)
			*dripResult_p = nullptr;

		IFCRResult *result = NULL;
		IFCRDripResult *dripResult = NULL;
		BOOL sensitive = ldfcr_CheckMatch(m_strTestFilePath, pfcrParam, result_p, dripResult_p, v_openFcr);

		return sensitive;
	}

	//通用检测文件夹接口
	BOOL detect_Dir(const TCHAR *path, void **result_p = NULL, void **dripResult_p = NULL,
		int num = 0, LPFCRPARAM pfcrParam = nullptr, int v_openFcr = _enTcrOpen::OPEN)
	{
		if (NULL == pILDFcr)
		{
			return FALSE;
		}

		std::vector <std::string> strVecFile;
		strVecFile.clear();
#ifdef WIN32
		std::wstring m_strTestFilePath;
		m_strTestFilePath.clear();
		getWStringFilePath(path, m_strTestFilePath);

		std::vector <std::wstring> wstrVecFile;
		getAllFilesDFS(m_strTestFilePath, wstrVecFile);


		for (auto it: wstrVecFile)
		{
			std::string path = WstringToUtf8(it);
			strVecFile.push_back(path);
		}
#else
		std::string m_strTestFilePath;
		m_strTestFilePath.clear();
		getStringFilePath(path, m_strTestFilePath);
		getAllFilesDFS(m_strTestFilePath, strVecFile);
#endif


		FCRPARAM fcrParam;
		fcrParam.use_all_rule = TRUE;   //启用所有规则
		fcrParam.target_class_code = 3; //达到或超过代码3，即停止
		fcrParam.output_detail = TRUE;  //要求返回时输出策略的细节信息
		fcrParam.devType = 1;
		fcrParam.opType = num;

		if (pfcrParam == nullptr)
			pfcrParam = &fcrParam;

		if (result_p)
			*result_p = nullptr;
		if (dripResult_p)
			*dripResult_p = nullptr;

		IFCRResult *result = NULL;
		IFCRDripResult *dripResult = NULL;
		BOOL sensitive = 0;
		std::vector<std::string> vecRes;
		vecRes.clear();
		for (auto it : strVecFile)
		{
			sensitive = ldfcr_CheckMatch(it, pfcrParam, result_p, dripResult_p, v_openFcr);
			if (sensitive == 1)
			{
				vecRes.push_back(it);
			}
		}
		std::cout << "TP: " << vecRes.size() << "/" << strVecFile.size() << std::endl;
		return !vecRes.empty();
	}


	//通用全局检测接口
	BOOL g_detect(const TCHAR *path, void **result_p, void **dripResult_p, int num = 0, LPFCRPARAM pfcrParam = nullptr, int v_openFcr = _enTcrOpen::OPEN)
	{
		std::string m_strTestFilePath;
		m_strTestFilePath.clear();
		getStringFilePath(path, m_strTestFilePath);

		FCRPARAM fcrParam;
		fcrParam.use_all_rule = TRUE;   //启用所有规则
		fcrParam.target_class_code = 3; //达到或超过代码3，即停止
		fcrParam.output_detail = TRUE;  //要求返回时输出策略的细节信息
		fcrParam.devType = 1;
		fcrParam.opType = num;

		if (pfcrParam == nullptr)
			pfcrParam = &fcrParam;

		if (result_p != nullptr)
			*result_p = nullptr;
		if (dripResult_p != nullptr)
			*dripResult_p = nullptr;

		BOOL sensitive = ldfcr_gCheckMatch(m_strTestFilePath, pfcrParam, result_p, dripResult_p, v_openFcr);
		
		return sensitive;
	}

	//设置密钥
	BOOL insert_key(char * key)
	{
		char * charDefalut = key;
		char * charNormal = key;

		if (!pILDFcr)
		{
			if (ldfcr_InitStartup())
			{
				ldfcr_CreateInstance((void **)&pILDFcr);
				if (NULL == pILDFcr)
				{
					return -1;
				}
			}
			else
			{
				return -1;
			}
		}

		BOOL AdvInit = pILDFcr->SetAdvInitParam(charNormal, charDefalut);
		return AdvInit;
	}

	//设置密钥
	BOOL g_insert_key(char * key)
	{
		//设置密码，写死
		//char * charNormal = "14fee2b0ffb3836b224721215778d1018a062ce16055d41e247fd87ad0ad021ae1";
		char * charNormal = key;
		BOOL AdvInit = ldfcr_SetAdvInitParam(charNormal);

		return AdvInit;
	}

	// 根据文件后缀名执行不同的类型检测路径
	BOOL ldfcr_CheckMatch(std::string& v_strTestFilePath,
		LPFCRPARAM   v_pFcrParam,
		void**       v_ppIFCRResult,
		void**       v_ppIFCRDripResult,int openTFcrType)
	{
		BOOL sensitive;
		if (v_strTestFilePath.find(".txt") != std::string::npos && openTFcrType)
		{
			dlplog_debug(g_log_handle, "executeTCR");
			long _fileSize = 0;
#ifdef WIN32		
			std::wstring _wstrTestFilePath = Utf8ToWstring(v_strTestFilePath);
			char* _fileContent = getFileContent((TCHAR *)_wstrTestFilePath.c_str(), &_fileSize);
#else
			char* _fileContent = getFileContent(v_strTestFilePath.c_str(), &_fileSize);
#endif
			if (_fileContent == NULL)
			{
				return false;
			}

			sensitive = pILDFcr->executeTCR(_fileContent, _fileSize, v_pFcrParam, v_ppIFCRResult, v_ppIFCRDripResult);
			
			delete[] _fileContent;
			_fileContent = NULL;
		}
		else
		{
			dlplog_debug(g_log_handle, "executeFCR");
			sensitive = pILDFcr->executeFCR(v_strTestFilePath.c_str(), v_pFcrParam, v_ppIFCRResult, v_ppIFCRDripResult);
		}
			
		return sensitive;
	}

	// 全局 根据文件后缀名执行不同的类型检测路径
	BOOL ldfcr_gCheckMatch(std::string& v_strTestFilePath,
		LPFCRPARAM   v_pFcrParam,
		void**       v_ppIFCRResult,
		void**       v_ppIFCRDripResult, int openTFcrType)
	{
		BOOL sensitive;
		if (v_strTestFilePath.find(".txt") != std::string::npos && openTFcrType)
		{
			dlplog_debug(g_log_handle, "executeTCR");
			long _fileSize = 0;
#ifdef WIN32		
			std::wstring _wstrTestFilePath = Utf8ToWstring(v_strTestFilePath);
			char* _fileContent = getFileContent((TCHAR *)_wstrTestFilePath.c_str(), &_fileSize);
#else
			char* _fileContent = getFileContent(v_strTestFilePath.c_str(), &_fileSize);
#endif
			if (_fileContent == NULL)
			{
				return false;
			}
			sensitive = ldfcr_ExecuteTCR(_fileContent, _fileSize, v_pFcrParam, v_ppIFCRResult, v_ppIFCRDripResult);

			delete[] _fileContent;
			_fileContent = NULL;
		}
		else
		{
			dlplog_debug(g_log_handle, "executeFCR");
			sensitive = ldfcr_ExecuteFCR(v_strTestFilePath.c_str(), v_pFcrParam, v_ppIFCRResult, v_ppIFCRDripResult);
		}

		return sensitive;
	}

	//后缀防篡改集合
	BOOL AntiModifiedSuffix(const TCHAR *path, int num = 0, LPFCRPARAM pfcrParam = nullptr)
	{
		if (NULL == pILDFcr)
		{
			return FALSE;
		}

		std::string m_strTestFilePath;
		m_strTestFilePath.clear();
		getStringFilePath(path, m_strTestFilePath);
#ifdef WIN32
		std::wstring _wstrTestFilePath = Utf8ToWstring(m_strTestFilePath);
		std::vector <std::wstring> vecRes;
#else
		std::vector <std::string> vecRes;
#endif

		bool isAntiChange = false;
		int iScanFile = 0;//统计扫描的个数
		int iAntiChangeNum = 0;//统计防篡改文件个数

		std::string newFilePath;
#ifdef WIN32
		std::wstring _wfilePath;
		std::vector <std::wstring> wstrVecFile;
		getAllFilesDFS(_wstrTestFilePath, wstrVecFile);
		std::vector<std::wstring> modifiedFiles;
#else
		std::wstring _filePath;
		std::vector <std::string> wstrVecFile;
		getAllFilesDFS(m_strTestFilePath, wstrVecFile);
		std::vector<std::string> modifiedFiles;
#endif
		FILE *logFile = fopen("AntiModifySuffix.txt", "w");
		if (logFile == nullptr)
		{
			std::cerr << "Failed to open log file." << std::endl;
			return FALSE;
		}

		for (const auto &filePath : wstrVecFile)
		{
			iScanFile++;
#ifdef WIN32
			std::wstring currentTime = getCurrentTimeString();
			std::wstring originalFilePath = filePath;
			_wfilePath = modifyFileExtension(originalFilePath, L".errorExt");
			if (_wfilePath.empty()) { // 检查重命名是否成功
				continue;
			}
			newFilePath = WstringToUtf8(_wfilePath);
#else
			std::string currentTime = getCurrentTimeString();
			std::string originalFilePath = filePath;
			newFilePath = modifyFileExtension(originalFilePath, ".errorExt");
			if (newFilePath.empty()) { // 检查重命名是否成功
				continue;
			}
#endif

			FCRPARAM fcrParam;
			fcrParam.use_all_rule = TRUE;   //启用所有规则
			fcrParam.target_class_code = 3; //达到或超过代码3，即停止
			fcrParam.output_detail = TRUE;  //要求返回时输出策略的细节信息
			fcrParam.devType = 1;
			fcrParam.opType = num;

			if (pfcrParam == nullptr)
				pfcrParam = &fcrParam;

			IFCRResult * result_p = NULL;
			BOOL sensitive = pILDFcr->executeFCR(newFilePath.c_str(), pfcrParam, (void**)&result_p, nullptr);
			if (result_p == NULL)
			{
				return false;
			}

			result_p->moveFirstPart();
			ICRPart * i = result_p->moveNextPart();
			if (NULL == i)
			{
#ifdef WIN32
				restoreFileExtension(_wfilePath, L".errorExt");// 恢复文件扩展名	
				fwprintf(logFile, L"%s File: %ls, Sensitive: %d, iAntiChange Num: %d\n", currentTime.c_str(), originalFilePath.c_str(), sensitive, isAntiChange);
#else
				restoreFileExtension(newFilePath, ".errorExt");// 恢复文件扩展名	
				fprintf(logFile, "%s File: %s, Sensitive: %d, iAntiChange Num: %d\n", currentTime.c_str(), originalFilePath.c_str(), sensitive, isAntiChange);
#endif
				continue;
			}

			modifiedFiles.push_back(originalFilePath);
			if (sensitive == 1)
			{
				isAntiChange = true;
				iAntiChangeNum++;
				vecRes.push_back(originalFilePath);
			}
#ifdef WIN32
			restoreFileExtension(_wfilePath, L".errorExt");
			//检测信息写入文件			
			fwprintf(logFile, L"%s File: %ls, Sensitive: %d, iAntiChange Num: %d\n", currentTime.c_str(), originalFilePath.c_str(), sensitive, isAntiChange);
#else
			restoreFileExtension(newFilePath, ".errorExt");
			//检测信息写入文件			
			fprintf(logFile, "%s File: %s, Sensitive: %d, iAntiChange Num: %d\n", currentTime.c_str(), originalFilePath.c_str(), sensitive, isAntiChange);
#endif
		}
		std::cout << "iAntiChangeNum:" << iAntiChangeNum << std::endl;
#ifdef WIN32
		fwprintf(logFile, L"The total number of scan file: %d,The total number of Anti-Modify file: %d\n", iScanFile, iAntiChangeNum);
		restoreFileExtension(_wfilePath, L".errorExt");
#else
		fprintf(logFile, "The total number of scan file: %d,The total number of Anti-Modify file: %d\n", iScanFile, iAntiChangeNum);
		restoreFileExtension(newFilePath, ".errorExt");
#endif
		fclose(logFile);
		std::cout << "TP: " << vecRes.size() << "/" << wstrVecFile.size() << std::endl;
		return TRUE;
	}

	// 文件后缀防篡改测试 
	BOOL FileSuffixPos(const TCHAR *path, int num = 0, LPFCRPARAM pfcrParam = nullptr)
	{
		if (NULL == pILDFcr)
		{
			return FALSE;
		}

#ifdef WIN32
		std::wstring m_strTestFilePath;
		getWStringFilePath(path, m_strTestFilePath);
#else
		std::string m_strTestFilePath = GetCurPath() + path;
#endif

		FCRPARAM fcrParam;
		fcrParam.use_all_rule = TRUE;   //启用所有规则
		fcrParam.target_class_code = 3; //达到或超过代码3，即停止
		fcrParam.output_detail = TRUE;  //要求返回时输出策略的细节信息
		fcrParam.devType = 1;
		fcrParam.opType = num;
		fcrParam.block_on_timeout = 1;	//超时阻断
		int m_time_input = 600000;// 10分钟
		fcrParam.timeout = m_time_input;//超时时间

		if (pfcrParam == nullptr)
			pfcrParam = &fcrParam;

		IFCRResult * result_p = NULL;
		BOOL sensitive = FALSE;
		int iScanFile = 0, iAntiChangeNum = 0;

	#ifdef WIN32
		std::vector<std::wstring> wstrVecFile;
		getAllFilesDFS(m_strTestFilePath, wstrVecFile);
		FILE *logFile = fopen("AntiModifySuffix.txt", "w");
		if (logFile == nullptr)
		{
			std::cerr << "Failed to open log file." << std::endl;
			return FALSE;
		}

		std::vector<std::wstring> vecRes;

		for (const auto &filePath : wstrVecFile)
		{
			iScanFile++;
			std::wstring currentTime = getCurrentTimeString();
			std::wstring originalFilePath = filePath;
			std::string path = WstringToUtf8(originalFilePath);
			std::wstring extractedExtensionBefore, extractedExtensionAfter;
			
			// 执行内容识别，获取原始格式
			sensitive = pILDFcr->executeFCR(path.c_str(), pfcrParam, (void**)&result_p, nullptr);
			if (result_p)
			{
				result_p->moveFirstPart();
				ICRPart *i = result_p->moveNextPart();
				if (i)
				{
					rapidjson::Json_Document doc;
					if (!doc.Parse<0>(i->getStrategyMatched()).HasParseError() && doc.HasMember("strategy") && doc["strategy"].IsArray())
					{
						for (auto& strategy : doc["strategy"].GetArray())
						{
							if (strategy.HasMember("classification") && strategy["classification"].IsArray())
							{
								for (auto& classification : strategy["classification"].GetArray())
								{
									if (classification.HasMember("rules") && classification["rules"].IsArray())
									{
										for (auto& rules : classification["rules"].GetArray())
										{
											if (rules.HasMember("keyWords") && rules["keyWords"].IsArray())
											{
												for (auto& keyWords : rules["keyWords"].GetArray())
												{
													if (keyWords.HasMember("keyWord"))
													{
														std::string extracted = keyWords["keyWord"].GetString();
														extractedExtensionBefore = std::wstring(extracted.begin(), extracted.end());
														break;
													}
												}
											}
										}
									}
								}
							}
						}
					}
				}
			}

			// 修改文件后缀
			std::wstring newFilePath = modifyFileExtension(originalFilePath, L".errorExt");
			if (newFilePath.empty()) continue;

			// 执行内容识别，获取修改后格式
			if (result_p && sensitive)
			{
				result_p->moveFirstPart();
				ICRPart *i = result_p->moveNextPart();
				if (i)
				{
					rapidjson::Json_Document doc;
					if (!doc.Parse<0>(i->getStrategyMatched()).HasParseError() && doc.HasMember("strategy") && doc["strategy"].IsArray())
					{
						for (auto& strategy : doc["strategy"].GetArray())
						{
							if (strategy.HasMember("classification") && strategy["classification"].IsArray())
							{
								for (auto& classification : strategy["classification"].GetArray())
								{
									if (classification.HasMember("rules") && classification["rules"].IsArray())
									{
										for (auto& rules : classification["rules"].GetArray())
										{
											if (rules.HasMember("keyWords") && rules["keyWords"].IsArray())
											{
												for (auto& keyWords : rules["keyWords"].GetArray())
												{
													if (keyWords.HasMember("keyWord"))
													{
														std::string extracted = keyWords["keyWord"].GetString();
														extractedExtensionAfter = std::wstring(extracted.begin(), extracted.end());
														break;
													}
												}
											}
										}
									}
								}
							}
						}
					}
				}
			}

			// 判断修改前后提取的格式是否一致
			bool isAntiChange = (extractedExtensionBefore == extractedExtensionAfter);
			if (isAntiChange)
			{
				iAntiChangeNum++;
			}

			// 记录日志
			fwprintf(logFile, L"File: %ls, Before: %ls, After: %ls, Sensitive: %d, Error Code: %d, isAntiChange: %d\n",
				originalFilePath.c_str(), extractedExtensionBefore.c_str(), extractedExtensionAfter.c_str(),
				sensitive, result_p ? result_p->getErrCode() : -1, isAntiChange);

			vecRes.push_back(originalFilePath);
			restoreFileExtension(newFilePath, L".errorExt"); // 恢复文件扩展名
		}
		std::cout << "TP: " << vecRes.size() << "/" << wstrVecFile.size() << std::endl;
		fwprintf(logFile, L"Total scanned files: %d, Total Anti-Modify files: %d\n", iScanFile, iAntiChangeNum);
		fclose(logFile);
	#else
		// Linux 版本 (类似修改)
	#endif

		return TRUE;
		}

#ifdef WIN32
	// base64解码
	BOOL detectBase64Decode(const TCHAR *path, int len, int num = 0, LPFCRPARAM pfcrParam = nullptr)
{
	if (NULL == pILDFcr)
	{
		return FALSE;
	}

#ifdef WIN32
	std::wstring m_strTestFilePath;
	getWStringFilePath(path, m_strTestFilePath);
#else
	std::string m_strTestFilePath = GetCurPath() + path;
#endif

	FCRPARAM fcrParam;
	fcrParam.use_all_rule = TRUE;   //启用所有规则
	fcrParam.target_class_code = 3; //达到或超过代码3，即停止
	fcrParam.output_detail = TRUE;  //要求返回时输出策略的细节信息
	fcrParam.devType = 1;
	fcrParam.opType = num;

	if (pfcrParam == nullptr)
		pfcrParam = &fcrParam;

	IFCRResult * result_p = NULL;
#ifdef WIN32
	std::wifstream inputFile(path);
#else
	std::ifstream inputFile(path);
#endif
	if (!inputFile.is_open())
	{
		std::cerr << "Failed to open input file." << std::endl;
		return FALSE;
	}

#ifdef WIN32
	std::wstring wstrLine;
	BOOL sensitive;
	while (std::getline(inputFile, wstrLine))
	{
		//每读一行，base64解密后再检测
		std::string base64_decoded = Base64Decode(std::string(wstrLine.begin(), wstrLine.end()));
		/*std::wstring decodedName = Utf8ToWstring(base64_decoded);
		sensitive = pILDFcr->executeTCRW(decodedName.c_str(), 0, pfcrParam, (void**)&result_p, nullptr);*/
#else
	std::string line;
	BOOL sensitive;
	while (std::getline(inputFile, line))
	{
		std::string base64_decoded = Base64Decode(line);
#endif
		sensitive = pILDFcr->executeTCR(base64_decoded.c_str(), 0, pfcrParam, (void**)&result_p, nullptr);
		result_p->moveFirstPart();
		ICRPart * i = result_p->moveNextPart();
		if (NULL == i)
		{
			continue;
		}
	}
#ifdef WIN32
	inputFile.close();
#endif
	return sensitive;
	}
#endif

#ifdef WIN32
	// 获取被阻断的泄露类型
	BOOL getOLT()
	{
		if (m_strategy != NULL)
		{
			//获取泄露类型
			char s[256] = { 0 };
			BOOL bRet = pILDFcr->getStopOutLossType(m_strategy, "", s, 256);
			if (!bRet)
			{
				return false;
			}
			return true;
		}
		return false;
	}
#endif

	// 允许文件外发申请测试
	BOOL outSendApply()
	{
		if (m_strategy != NULL)
		{
			rapidjson::Json_Document doc;
			if (doc.Parse<0>(m_strategy).HasParseError())
			{
				return false;
			}

			if (!doc.Parse<0>(m_strategy).IsObject())
			{
				return false;
			}

			if (doc.HasMember("respondRule") && doc["respondRule"].IsArray())
			{
				rapidjson::Json_Value& respondRuleArry = doc["respondRule"];
				for (unsigned int i = 0; i < respondRuleArry.Size(); i++)
				{
					rapidjson::Json_Value &respondRule = respondRuleArry[i];
					// 是否阻断数据外发
					if (!(respondRule.HasMember("stopOutgoing") && respondRule["stopOutgoing"].IsInt()))
					{
						return false;
					}
					// 是否允许文件外发申请
					if (!(respondRule.HasMember("outSendApply")))
					{
						return false;
					}
					// 阻断外发的文件类型
					if (!(respondRule.HasMember("stopOutgoingEx") && respondRule["stopOutgoingEx"].IsInt()))
					{
						return false;
					}
				}

				return true;
			}
		}
		return false;
	}

	//检测结果json串中输出响应规则中泄露方式  
	BOOL getResultLossType(IFCRResult * result_p, BOOL sensitive)
	{
		if (result_p == NULL)
		{
			return false;
		}
		
		result_p->moveFirstPart();
		ICRPart * i = result_p->moveNextPart();
		if (NULL == i)
		{
			return false;
		}

		rapidjson::Json_Document doc;
		if (doc.Parse<0>(i->getStrategyMatched()).HasParseError())
		{
			return false;
		}

		if (doc.HasMember("strategy") && doc["strategy"].IsArray())
		{
			rapidjson::Json_Value& strategy = doc["strategy"];
			for (unsigned int i = 0; i < strategy.Size(); ++i)
			{
				rapidjson::Json_Value & strategy_obj = strategy[i];
				
				// 是否含有泄露类型
				if (strategy_obj.HasMember("respond") && strategy_obj["respond"].IsArray())
				{
					rapidjson::Json_Value & respond = strategy_obj["respond"];
					for (unsigned int i = 0; i < respond.Size(); ++i)
					{
						rapidjson::Json_Value &respond_obj = respond[i];
						if (respond_obj.HasMember("lossType") && respond_obj["lossType"].IsString())
						{
							return true;
						}
					}
				}
			}
		}
		return false;
	}

	// 验证Json剪切长度的准确性
	BOOL JugCutJsonSize(const TCHAR *path, unsigned int iBuffLen, int num = 0)
	{
		long fileSize = 0;
		char* fileContent = nullptr;
#ifdef WIN32
		std::wstring m_strTestFilePath;
		getWStringFilePath(path, m_strTestFilePath);
		fileContent = getFileContent((TCHAR *)m_strTestFilePath.c_str(), &fileSize);
#else
		std::string str_file = path;
		std::string strFilePath = GetCurPath() + str_file;
		fileContent = getFileContent(strFilePath.c_str(), &fileSize);
#endif
		if (fileContent == nullptr)
		{
			std::cerr << "Failed to open the file." << std::endl;
			return FALSE;
		}
		std::string strLogContent(fileContent, fileSize);
		const char* json = strLogContent.c_str();

		// 调用json剪切函数
		const int bufferSize = 1024;
		char pcUTF8str_Out[bufferSize] = { 0 };

		//cutResult表示剪切后数据存入是否成功；
		//json.json文本的iBuffLen返回长度为236，所以传入的iBuffLen缓冲内存不能低于236；
		BOOL cutResult = ldfcr_CutJsontoSize(json, pcUTF8str_Out, iBuffLen, 1);
		if (!cutResult)
		{
			std::cerr << "Failed to cut JSON to size." << std::endl;
			delete[] fileContent;
			return FALSE;
		}

		// 验证剪切的长度是否正确
		if (cutResult)
		{
			int actualCutLength = strlen(pcUTF8str_Out);
			if (actualCutLength != iBuffLen)
			{
				std::cerr << "Cut length mismatch: expected " << iBuffLen << ", got " << actualCutLength << std::endl;
				std::cerr << "Cut content: " << pcUTF8str_Out << std::endl;
				delete[] fileContent;
				return FALSE;
			}
		}
		delete[] fileContent;
		return cutResult;
	}

	//判断返回的JSON中是否包含 ruletype 字段	
	BOOL JugExsitRuletypeInJson(IFCRResult * result_p, BOOL sensitive)
	{
		if (sensitive && result_p)
		{
			result_p->moveFirstPart();
			ICRPart * icr = result_p->moveNextPart();
			if (NULL == icr)
			{
				return false;
			}
			rapidjson::Json_Document doc;
			if (doc.Parse<0>(icr->getStrategyMatched()).HasParseError())
			{
				return false;
			}
			if (doc.HasMember("strategy") && doc["strategy"].IsArray())
			{
				rapidjson::Json_Value& strategy = doc["strategy"];
				for (unsigned int i = 0; i < strategy.Size(); ++i)
				{
					rapidjson::Json_Value & strategy_obj = strategy[i];
					// 是否含有泄露类型
					if (strategy_obj.HasMember("classification") && strategy_obj["classification"].IsArray())
					{
						rapidjson::Json_Value & classification = strategy_obj["classification"];
						for (unsigned int i = 0; i < classification.Size(); ++i)
						{
							rapidjson::Json_Value &classification_obj = classification[i];
							if (classification_obj.HasMember("rules") && classification_obj["rules"].IsArray())
							{
								rapidjson::Json_Value & rules_array = classification_obj["rules"];
								for (unsigned int k = 0; k < rules_array.Size(); ++k)
								{
									rapidjson::Value& rule_obj = rules_array[k];
									if (rule_obj.HasMember("ruletype"))
									{
										return true;
									}
								}
							}
						}
					}
				}
			}
		}

		return FALSE;
	}

	//响应规则相关参数设置
	typedef struct RespondParam
	{
		int m_ErrorCode;
		unsigned int m_iRulesId;
	}RESPONDPARAM,*PRESPONDPARAM;

	//by lwf  2025.05.22   自定义响应规则 通用接口 
	BOOL checkRespondRulesName(const TCHAR *path, PRESPONDPARAM v_respParam,int num = 0, LPFCRPARAM pfcrParam = nullptr)
	{
		if (NULL == pILDFcr)
		{
			return FALSE;
		}

		std::string m_strTestFilePath;
		m_strTestFilePath.clear();
		getStringFilePath(path, m_strTestFilePath);

		//带密码才需要设置
		if (v_respParam->m_ErrorCode == LDFCR_PWDERROR)
		{
			std::string str_password = "123456";
			std::string strJson = ("{\"password\": [\"" + str_password + "\"]}");
			BOOL Seyt = 0;
			Seyt = pILDFcr->SetFilterGlobalParamer(strJson.c_str());
		}
		
		long _fileSize = 0;
#ifdef WIN32	
		std::wstring str_file = L"test\\strategy_crr.json";
		char* _fileContent = getFileContent((TCHAR *)str_file.c_str(), &_fileSize);
#else
		std::string str_Datafile = "test/strategy_crr.json";
		std::string _strStrategyFilePath;
		getStringFilePath(str_Datafile.c_str(), _strStrategyFilePath);

		char* _fileContent = getFileContent(_strStrategyFilePath.c_str(), &_fileSize);
#endif
		if (_fileContent == NULL)
		{
			std::cerr << "Failed to open the file." << std::endl;
			return FALSE;
		}

		std::string strLogContent(_fileContent, _fileSize);
		//释放内存
		delete[] _fileContent;

		//解析JSON内容
		rapidjson::Document document;
		document.Parse(strLogContent.c_str());
		if (!document.HasParseError())
		{
			const char* m_iRulesName = "響應規則";
			unsigned int m_iRulesId = v_respParam->m_iRulesId;
			BOOL result = pILDFcr->AddInlineRespond(strLogContent.c_str(), m_iRulesName, m_iRulesId);
		}
		else
		{
			std::cerr << "Failed to parse CustomRespondRules JSON content." << std::endl;
			return FALSE;
		}

		IFCRResult * result_p = NULL;
		IFCRDripResult * dripResult_p = NULL;
		BOOL sensitive = ldfcr_CheckMatch(m_strTestFilePath, pfcrParam, (void**)&result_p, NULL, _enTcrOpen::OPEN);

		// 解析匹配的策略
		if (sensitive && result_p)
		{
			result_p->moveFirstPart();
			ICRPart* part = nullptr;
			while ((part = result_p->moveNextPart()) != nullptr)
			{
				std::string matchedStrategyJson = part->getStrategyMatched();
				//std::cout << "Matched Strategy JSON:" << matchedStrategyJson << std::endl;
				// 使用 RapidJSON 解析 JSON 字符串
				rapidjson::Document strategyDoc;
				strategyDoc.Parse(matchedStrategyJson.c_str());

				// 对匹配的策略 JSON 进行处理，检查是否包含指定规则名
				if (strategyDoc.HasMember("name") && strategyDoc["name"].IsString())
				{
					std::string nameValue = strategyDoc["name"].GetString();
					if (!nameValue.compare("響應規則"))
					{
						// 未找到指定规则名			
						CLEAN_UP(result_p, dripResult_p)
						return FALSE;
					}
					else
					{
						if (v_respParam->m_ErrorCode == result_p->getErrCode())
						{
							CLEAN_UP(result_p, dripResult_p)
							return TRUE;
						}
						CLEAN_UP(result_p, dripResult_p)
						return FALSE;
					}
				}
			}
		}

		return sensitive;
	}

	// 案例测试的敏感点与检测后光亮的位置匹配不上
	BOOL checkKeyWordsInResultText(IFCRResult * result_p, BOOL sensitive)
	{
		if (sensitive && result_p)
		{
			result_p->moveFirstPart();
			ICRPart * icr = result_p->moveNextPart();
			if (NULL == icr)
			{
				return false;
			}
			rapidjson::Json_Document doc;
			if (doc.Parse<0>(icr->getStrategyMatched()).HasParseError())
			{
				return false;
			}
			if (doc.HasMember("strategy") && doc["strategy"].IsArray())
			{
				rapidjson::Json_Value& strategy = doc["strategy"];
				for (unsigned int i = 0; i < strategy.Size(); ++i)
				{
					rapidjson::Json_Value & strategy_obj = strategy[i];
					// 是否含有泄露类型
					if (strategy_obj.HasMember("classification") && strategy_obj["classification"].IsArray())
					{
						rapidjson::Json_Value & classification = strategy_obj["classification"];
						for (unsigned int i = 0; i < classification.Size(); ++i)
						{
							rapidjson::Json_Value &classification_obj = classification[i];
							if (classification_obj.HasMember("rules") && classification_obj["rules"].IsArray())
							{
								rapidjson::Json_Value & rules_array = classification_obj["rules"];
								rapidjson::Json_Value & rules = rules_array[0];
								rapidjson::Json_Value & keyWords_array = rules["keyWords"];
								rapidjson::Json_Value & keyWords = keyWords_array[0];
								if (keyWords.HasMember("keyWord") && keyWords["keyWord"].IsString())
								{
									std::string result = keyWords["keyWord"].GetString();
									std::string str(icr->getTextTrimed());
									int res = str.find(result);
									if (res != std::string::npos)
									{
										return TRUE;
									}
								}
							}
						}
					}
				}
			}
		}
		return FALSE;
	}

	//测试高亮位置和敏感匹配的计数点是否能匹配上 
	BOOL checkShiftPos(IFCRResult * result_p, BOOL sensitive,int ipos)
	{
		if (sensitive && result_p)
		{
			result_p->moveFirstPart();
			ICRPart * i = result_p->moveNextPart();
			if (NULL == i)
			{
				return false;
			}
			rapidjson::Json_Document doc;
			if (doc.Parse<0>(i->getStrategyMatched()).HasParseError())
			{
				return false;
			}
			if (doc.HasMember("strategy") && doc["strategy"].IsArray())
			{
				rapidjson::Json_Value& strategy = doc["strategy"];
				for (unsigned int i = 0; i < strategy.Size(); ++i)
				{
					rapidjson::Json_Value & strategy_obj = strategy[i];
					// 是否含有泄露类型
					if (strategy_obj.HasMember("classification") && strategy_obj["classification"].IsArray())
					{
						rapidjson::Json_Value & classification = strategy_obj["classification"];
						for (unsigned int i = 0; i < classification.Size(); ++i)
						{
							rapidjson::Json_Value &classification_obj = classification[i];
							if (classification_obj.HasMember("rules") && classification_obj["rules"].IsArray())
							{
								rapidjson::Json_Value & rules_array = classification_obj["rules"];
								rapidjson::Json_Value & rules = rules_array[0];
								rapidjson::Json_Value & keyWords_array = rules["keyWords"];
								rapidjson::Json_Value & keyWords = keyWords_array[0];
								rapidjson::Json_Value & pos_array = keyWords["pos"];
								rapidjson::Json_Value & pos = pos_array[0];
								if (pos.HasMember("charPos") && pos["charPos"].IsInt())
								{
									if (ipos == pos["charPos"].GetInt())
									{
										return true;
									}
									int ims = pos["charPos"].GetInt();
									std::cout << ims << std::endl;
								}
							}
						}
					}
				}
			}
		}
		return false;
	}

	// 高亮测试 checktimes == 2
	BOOL checkShiftPos2(IFCRResult * result_p, BOOL sensitive,int ipos1, int ipos2)
	{
		if (sensitive && result_p)
		{
			result_p->moveFirstPart();
			ICRPart * _icpPart = result_p->moveNextPart();
			if (NULL == _icpPart)
			{
				return false;
			}
#ifdef WIN32
			if (NULL != _icpPart)
			{
				std::string gbTextTrimed = _icpPart->getTextTrimed();
				if (gbTextTrimed.empty())
				{
					dlplog_info(g_log_handle, "Trimmed text is empty.");
				}
				else
				{
					dlplog_info(g_log_handle, "gbTextTrimed:%s", gbTextTrimed.c_str());
				}
			}
#endif
			rapidjson::Json_Document doc;
			if (doc.Parse<0>(_icpPart->getStrategyMatched()).HasParseError())
			{
				return false;
			}
			if (doc.HasMember("strategy") && doc["strategy"].IsArray())
			{
				rapidjson::Json_Value& strategy = doc["strategy"];
				for (unsigned int i = 0; i < strategy.Size(); ++i)
				{
					rapidjson::Json_Value & strategy_obj = strategy[i];
					// 是否含有泄露类型
					if (strategy_obj.HasMember("classification") && strategy_obj["classification"].IsArray())
					{
						rapidjson::Json_Value & classification = strategy_obj["classification"];
						for (unsigned int i = 0; i < classification.Size(); ++i)
						{
							rapidjson::Json_Value &classification_obj = classification[i];
							if (classification_obj.HasMember("rules") && classification_obj["rules"].IsArray())
							{
								rapidjson::Json_Value & rules_array = classification_obj["rules"];
								rapidjson::Json_Value & rules = rules_array[0];
								rapidjson::Json_Value & keyWords_array = rules["keyWords"];
								rapidjson::Json_Value & keyWords = keyWords_array[0];
								rapidjson::Json_Value & pos_array = keyWords["pos"];

								rapidjson::Json_Value & pos1 = pos_array[0];
								if (pos1.HasMember("charPos") && pos1["charPos"].IsInt())
								{
									if (ipos1 == pos1["charPos"].GetInt())
									{
										return true;
									}
									int ims = pos1["charPos"].GetInt();
									std::cout << ims << std::endl;
								}

								rapidjson::Json_Value & pos2 = pos_array[1];
								if (pos2.HasMember("charPos") && pos2["charPos"].IsInt())
								{
									if (ipos2 == pos2["charPos"].GetInt())
									{
										return true;
									}
									int ims = pos2["charPos"].GetInt();
									std::cout << ims << std::endl;
								}
							}
						}
					}
				}
			}
		}
		return false;
	}

	// 多敏感文本测试
	BOOL checkTripleShiftPos(IFCRResult * result_p, BOOL sensitive, int ipos1, int ipos2, int ipos3)
	{
		if (sensitive && result_p)
		{
			result_p->moveFirstPart();
			ICRPart * i = result_p->moveNextPart();
			if (NULL == i)
			{
				return false;
			}
			rapidjson::Json_Document doc;
			if (doc.Parse<0>(i->getStrategyMatched()).HasParseError())
			{
				return false;
			}
			if (doc.HasMember("strategy") && doc["strategy"].IsArray())
			{
				rapidjson::Json_Value& strategy = doc["strategy"];
				for (unsigned int i = 0; i < strategy.Size(); ++i)
				{
					rapidjson::Json_Value & strategy_obj = strategy[i];
					// 是否含有泄露类型
					if (strategy_obj.HasMember("classification") && strategy_obj["classification"].IsArray())
					{
						rapidjson::Json_Value & classification = strategy_obj["classification"];
						for (unsigned int i = 0; i < classification.Size(); ++i)
						{
							rapidjson::Json_Value &classification_obj = classification[i];
							if (classification_obj.HasMember("rules") && classification_obj["rules"].IsArray())
							{
								rapidjson::Json_Value & rules_array = classification_obj["rules"];
								rapidjson::Json_Value & rules = rules_array[0];
								rapidjson::Json_Value & keyWords_array = rules["keyWords"];
								rapidjson::Json_Value & keyWords = keyWords_array[0];
								rapidjson::Json_Value & pos_array = keyWords["pos"];
								rapidjson::Json_Value & pos = pos_array[0];
								if (pos.HasMember("charPos") && pos["charPos"].IsInt())
								{
									//std::cout <<"1: "<< pos["charPos"].GetInt() << std::endl;
									if (ipos1 != pos["charPos"].GetInt())
									{
										return false;
									}
								}
								keyWords = keyWords_array[1];
								pos_array = keyWords["pos"];
								pos = pos_array[0];
								if (pos.HasMember("charPos") && pos["charPos"].IsInt())
								{
									//std::cout << "2: " << pos["charPos"].GetInt() << std::endl;
									if (ipos2 != pos["charPos"].GetInt())
									{
										return false;
									}

								}
								keyWords = keyWords_array[2];
								pos_array = keyWords["pos"];
								pos = pos_array[0];
								if (pos.HasMember("charPos") && pos["charPos"].IsInt())
								{
									//std::cout << "3: " << pos["charPos"].GetInt() << std::endl;
									if (ipos3 != pos["charPos"].GetInt())
									{
										return false;
									}
								}
								return true;
							}
						}
					}
				}
			}
		}
		return false;
	}

	//by Kai 2022.11.21
	//加密文本应返回 - 4 超过500MB应返回 - 3
	BOOL checkErCode(IFCRResult * result_p,int errorcode)
	{
		if (result_p)
		{
			if (errorcode == result_p->getErrCode())
			{
				std::cout << result_p->getErrCode() << std::endl;
				return TRUE;
			}
			else
			{
				std::cout << result_p->getErrCode() << std::endl;
				return FALSE;
			}
		}
		return FALSE;
	}

	// 验证返回的错误值
	BOOL get_error_code(IFCRResult * result_p,BOOL sensitive, int errCode)
	{
		if (!sensitive || result_p == NULL) 
		{
			return FALSE;
		}

		result_p->moveFirstPart();
		ICRPart * i = result_p->moveNextPart();
		if (NULL == i)
		{
			return FALSE;
		}

		if ((result_p->getErrCode()) != errCode)
		{
			return FALSE;
		}

		return TRUE;
	}

	// 验证标签策略返回结果是否包含 TagInfo 字段
	BOOL checkTagInfoExists(IFCRResult * result_p, BOOL sensitive )
	{
		if (result_p!=NULL && sensitive)
		{
			result_p->moveFirstPart();
			ICRPart * i = result_p->moveNextPart();
			if (NULL == i)
			{
				return false;
			}
			rapidjson::Json_Document doc;
			if (doc.Parse<0>(i->getStrategyMatched()).HasParseError())
			{
				return false;
			}
			if (doc.HasMember("strategy") && doc["strategy"].IsArray())
			{
				rapidjson::Json_Value& strategy = doc["strategy"];
				for (unsigned int i = 0; i < strategy.Size(); ++i)
				{
					rapidjson::Json_Value & strategy_obj = strategy[i];
					// 检查是否存在 tagScan 字段
					if (strategy_obj.HasMember("tagScan"))
					{
						return true;
					}
				}
			}
		}
		
		return false;
	}

	// 验证 标签策略 返回结果只包含 标签策略 相关内容 
	BOOL onlyReturnTagRespond(const TCHAR *path, int TagRespond,int ResType, int num = 0, LPFCRPARAM pfcrParam = nullptr)
	{
		if (NULL == pILDFcr)
		{
			return FALSE;
		}

		std::string m_strTestFilePath;
		m_strTestFilePath.clear();
		getStringFilePath(path, m_strTestFilePath);

		FCRPARAM fcrParam;
		fcrParam.use_all_rule = TRUE;   //启用所有规则
		fcrParam.target_class_code = 3; //达到或超过代码3，即停止
		fcrParam.output_detail = TRUE;  //要求返回时输出策略的细节信息
		fcrParam.devType = 1;
		fcrParam.opType = num;
		fcrParam.onlyreturnTagRespond = TagRespond;//只保留标签部分结果

		if (pfcrParam == nullptr)
			pfcrParam = &fcrParam;

		IFCRResult * result_p = NULL;
		BOOL sensitive = ldfcr_CheckMatch(m_strTestFilePath, pfcrParam, (void**)&result_p, NULL, _enTcrOpen::CLOSE);
		if (!sensitive)
		{
			return FALSE;
		}
		if (result_p != NULL && sensitive)
		{
			result_p->moveFirstPart();
			ICRPart * i = result_p->moveNextPart();
			unsigned int ui = i->getStrategyMatchedType();
			if (ui == ResType)
			{
				return TRUE;
			}

			result_p->Release();
		}
		
		return FALSE;
	}

	// 验证 常规策略和标签策略合并后的结果 宽字符版本
//	BOOL GetMergeStrategyW(const TCHAR *RegularStrategyPath, const TCHAR *TagStrategyPath, unsigned int RegularStrategyID, unsigned int TagStrategyID, int num = 0, LPFCRPARAM pfcrParam = nullptr)
//	{
//#ifdef WIN32
//		std::wstring m_wstrRegularFilePath;
//		std::wstring m_wstrTagStrategyPath;
//		getWStringFilePath(RegularStrategyPath, m_wstrRegularFilePath);
//		getWStringFilePath(TagStrategyPath, m_wstrTagStrategyPath);
//
//		// 读取文件内W
//		std::ifstream regularFile(m_wstrRegularFilePath);
//		std::ifstream tagFile(m_wstrTagStrategyPath);
//
//		if (!regularFile.is_open() || !tagFile.is_open()) {
//			return FALSE;
//		}
//
//		// 正确的字符串构造方式
//		std::string regularTemp;
//		regularTemp.assign(
//			(std::istreambuf_iterator<char>(regularFile)),
//			std::istreambuf_iterator<char>()
//		);
//		regularFile.close();
//
//		std::string tagTemp;
//		tagTemp.assign(
//			(std::istreambuf_iterator<char>(tagFile)),
//			std::istreambuf_iterator<char>()
//		);
//		tagFile.close();
//
//		// 将UTF-8转换为wstring
//		int regularLen = MultiByteToWideChar(CP_UTF8, 0, regularTemp.c_str(), -1, NULL, 0);
//		std::wstring regularContent(regularLen, 0);
//		MultiByteToWideChar(CP_UTF8, 0, regularTemp.c_str(), -1, &regularContent[0], regularLen);
//
//		int tagLen = MultiByteToWideChar(CP_UTF8, 0, tagTemp.c_str(), -1, NULL, 0);
//		std::wstring tagContent(tagLen, 0);
//		MultiByteToWideChar(CP_UTF8, 0, tagTemp.c_str(), -1, &tagContent[0], tagLen);
//
//		WCHAR mergeResult[MAX_FILE_COUNT] = { 0 };
//		unsigned int mergeResultLength = MAX_FILE_COUNT;
//		BOOL sensitive = ldfcr_MergeStrategyW(regularContent.c_str(), tagContent.c_str(), mergeResult, mergeResultLength);//传入的两个策略和合并的策略中文不乱码
//
//		std::string strMergeResult = PublicUnicodeToUTF8(mergeResult);
//		dlplog_info(g_log_handle, "[%d] %s", __LINE__, strMergeResult.c_str());
//
//		//解析合并的json正确性
//		rapidjson::Document doc;
//		if (doc.Parse(strMergeResult.c_str()).HasParseError())
//		{
//			dlplog_info(g_log_handle, "[%d] JSON解析错误", __LINE__);
//			return FALSE;
//		}
//		if (doc.HasMember("strategy") && doc["strategy"].IsArray())
//		{
//			const rapidjson::Value& strategyArray = doc["strategy"];
//			int foundIds = 0;
//			for (rapidjson::SizeType i = 0; i < strategyArray.Size(); i++)
//			{
//				const rapidjson::Value& strategy = strategyArray[i];
//				if (strategy.HasMember("id") && strategy["id"].IsInt())
//				{
//					int id = strategy["id"].GetInt();
//					if (id == RegularStrategyID || id == TagStrategyID)
//					{
//						foundIds++;
//					}
//				}
//			}
//			// 如果找到了两个策略ID，则返回true
//			sensitive = (foundIds == 2);
//		}
//		else
//		{
//			sensitive = FALSE;
//		}
//		return sensitive;
//#endif
//	}
//
	// 验证 常规策略和标签策略合并后的结果 
	BOOL GetMergeStrategy(const TCHAR *RegularStrategyPath, const TCHAR *TagStrategyPath, unsigned int RegularStrategyID, unsigned int TagStrategyID, int num = 0, LPFCRPARAM pfcrParam = nullptr)
	{
#ifdef WIN32
		std::wstring m_wstrRegularFilePath;
		std::wstring m_wstrTagStrategyPath;
		getWStringFilePath(RegularStrategyPath, m_wstrRegularFilePath);
		getWStringFilePath(TagStrategyPath, m_wstrTagStrategyPath);
#else
		std::string str_Regularfile = RegularStrategyPath;
		std::string str_Tagfile = TagStrategyPath;
		std::string m_strRegularFilePath = GetCurPath() + str_Regularfile;
		std::string m_strTagFilePath = GetCurPath() + str_Tagfile;
#endif
		// 读取文件内容
#ifdef WIN32
		std::ifstream regularFile(m_wstrRegularFilePath);
		std::ifstream tagFile(m_wstrTagStrategyPath);
#else
		std::ifstream regularFile(m_strRegularFilePath.c_str());
		std::ifstream tagFile(m_strTagFilePath.c_str());
#endif
		if (!regularFile.is_open() || !tagFile.is_open()) {
			return FALSE;
		}

		std::string regularTemp;
		regularTemp.assign(
			(std::istreambuf_iterator<char>(regularFile)),
			std::istreambuf_iterator<char>()
		);
		regularFile.close();

		std::string tagTemp;
		tagTemp.assign(
			(std::istreambuf_iterator<char>(tagFile)),
			std::istreambuf_iterator<char>()
		);
		tagFile.close();

		// 直接使用 std::string 作为策略内容
		const char* regularContent = regularTemp.c_str();
		const char* tagContent = tagTemp.c_str();

		char mergeResult[MAX_FILE_COUNT] = { 0 };
		unsigned int mergeResultLength = MAX_FILE_COUNT;

		// 调用 ldfcr_MergeStrategy 接口
		BOOL sensitive = ldfcr_MergeStrategy(regularContent, tagContent, mergeResult, mergeResultLength);

		// 直接使用 mergeResult 作为日志输出
		dlplog_info(g_log_handle, "[%d] %s", __LINE__, mergeResult);

		// 解析合并的json正确性
		rapidjson::Document doc;
		if (doc.Parse(mergeResult).HasParseError())
		{
			dlplog_info(g_log_handle, "[%d] JSON解析错误", __LINE__);
			return FALSE;
		}
		if (doc.HasMember("strategy") && doc["strategy"].IsArray())
		{
			const rapidjson::Value& strategyArray = doc["strategy"];
			int foundIds = 0;
			for (rapidjson::SizeType i = 0; i < strategyArray.Size(); i++)
			{
				const rapidjson::Value& strategy = strategyArray[i];
				if (strategy.HasMember("id") && strategy["id"].IsInt())
				{
					int id = strategy["id"].GetInt();
					if (id == RegularStrategyID || id == TagStrategyID)
					{
						foundIds++;
					}
				}
			}
			// 如果找到了两个策略ID，则返回true
			sensitive = (foundIds == 2);
		}
		else
		{
			sensitive = FALSE;
		}
		return sensitive;
	}

	// 验证 匹配到的策略类型 
	BOOL getStrategyMatchedType(const TCHAR *path, unsigned int iStrategyMatchedType, int num = 0, LPFCRPARAM pfcrParam = nullptr)
	{
		if (NULL == pILDFcr)
		{
			return FALSE;
		}

		std::string m_strTestFilePath;
		m_strTestFilePath.clear();
		getStringFilePath(path, m_strTestFilePath);

		FCRPARAM fcrParam;
		fcrParam.use_all_rule = TRUE;   //启用所有规则
		fcrParam.target_class_code = 3; //达到或超过代码3，即停止
		fcrParam.output_detail = TRUE;  //要求返回时输出策略的细节信息
		fcrParam.devType = 1;
		fcrParam.opType = num;

		if (pfcrParam == nullptr)
			pfcrParam = &fcrParam;

		IFCRResult * result_p = NULL;
		BOOL sensitive = ldfcr_CheckMatch(m_strTestFilePath, pfcrParam, (void**)&result_p, NULL, _enTcrOpen::CLOSE);
		if (!sensitive || result_p == NULL)
		{
			return FALSE;
		}

		result_p->moveFirstPart();
		ICRPart * i = result_p->moveNextPart();
		if (NULL == i)
		{
			return false;
		}

		unsigned int iType = i->getStrategyMatchedType();
		if (iType == iStrategyMatchedType)
		{
			return true;
		}
		result_p->Release();
		return false;
	}

	// 验证 获取到的文件类型
	BOOL getFileExt(IFCRResult * result_p, const char* targetExt)
	{
		if (result_p == NULL) {
			return FALSE;
		}
		result_p->moveFirstPart();
		ICRPart * i = result_p->moveNextPart();
		if (NULL == i)
		{
			return FALSE;
		}
		
		// 获取文件扩展名
		const char* fileExt = i->getFileExt();
		if (fileExt == nullptr || strcmp(fileExt, targetExt) != 0)
		{
			return FALSE;
		}
		return TRUE;
	}

	//关键字偏移量校对
	BOOL checkPos(IFCRResult * result_p, BOOL sensitive)
	{
		if (sensitive && result_p != NULL)
		{
			result_p->moveFirstPart();
			ICRPart * pIcrPart = result_p->moveNextPart();
			if (NULL == pIcrPart)
			{
				return false;
			}
			rapidjson::Json_Document doc;
			if (doc.Parse<0>(pIcrPart->getStrategyMatched()).HasParseError())
			{
				return false;
			}
			if (doc.HasMember("strategy") && doc["strategy"].IsArray())
			{
				rapidjson::Json_Value& strategy = doc["strategy"];
				for (unsigned int i = 0; i < strategy.Size(); ++i)
				{
					rapidjson::Json_Value & strategy_obj = strategy[i];
					// 是否含有泄露类型
					if (strategy_obj.HasMember("classification") && strategy_obj["classification"].IsArray())
					{
						rapidjson::Json_Value & classification = strategy_obj["classification"];
						for (unsigned int j = 0; j < classification.Size(); ++j)
						{
							rapidjson::Json_Value &classification_obj = classification[j];
							if (classification_obj.HasMember("rules") && classification_obj["rules"].IsArray())
							{
								rapidjson::Json_Value & rules_array = classification_obj["rules"];
								rapidjson::Json_Value & rules = rules_array[0];
								rapidjson::Json_Value & keyWords_array = rules["keyWords"];
								rapidjson::Json_Value & keyWords = keyWords_array[0];
								rapidjson::Json_Value & pos_array = keyWords["pos"];
								rapidjson::Json_Value & pos = pos_array[0];
								if (pos.HasMember("charPos") && pos["charPos"].IsInt())
								{
									if (2 == pos["charPos"].GetInt())
									{
										return true;
									}
								}
							}
						}
					}
				}
			}
		}
	
		return false;
	}

	// 结果只有常规检测 - 重复 4096/6 次
	BOOL detect_text(const char *content, int len, void **result_p, void **dripResult_p, int num = 0)
	{
		if (NULL == pILDFcr)
		{
			return FALSE;
		}

		FCRPARAM fcrParam;
		fcrParam.use_all_rule = TRUE;   //启用所有规则
		fcrParam.target_class_code = 3; //达到或超过代码3，即停止
		fcrParam.output_detail = TRUE;  //要求返回时输出策略的细节信息
		fcrParam.devType = 1;
		fcrParam.opType = num;
		fcrParam.fcrInfo = "{\"IP\": \"***********\"}";

		if (result_p != nullptr)
			*result_p = nullptr;
		if (dripResult_p != nullptr)
			*dripResult_p = nullptr;

		BOOL sensitive = pILDFcr->executeTCR(content, -1, &fcrParam, result_p, dripResult_p);

		return sensitive;
	}

	//测试 UTF-8 文本截断是否正常
	BOOL checkUTF8Crop(IFCRDripResult * dripResult, BOOL sensitive)
	{
		if (sensitive && dripResult != NULL)
		{
			dripResult->moveFirstIncident();
			ICRIncident *incident;
			while ((incident = dripResult->moveNextIncident()) != nullptr)
			{
				incident->moveFirstMatch();
				ICRMatch *match = NULL;
				while ((match = incident->moveNextMatch()) != nullptr)
				{
					const char *text = match->getTextTrimed();
					EXPECT_TRUE(FIRSTCHAR(text[0]));
					return IsUTF8EndByte(text, text + strlen(text) - 1);
				}
			}
		}
		return false;
	}

	//零星检测 检测 返回 json 能否解析成功
	BOOL getDripResult(IFCRDripResult * dripResult_p, BOOL sensitive)
	{
		if (sensitive && NULL != dripResult_p)
		{
			dripResult_p->moveFirstIncident();
			ICRIncident *incident = NULL;
			while ((incident = dripResult_p->moveNextIncident()) != NULL)
			{
				if (strlen(incident->getStrategyName()) <= 0)
				{
					return FALSE;
				}
				incident->moveFirstMatch();
				while ((incident->moveNextMatch()) != NULL)
				{
					rapidjson::Json_Document doc;
					if (doc.Parse<0>(incident->getStrategyMatched()).HasParseError())
					{
						return FALSE;
					}
					std::string gbStrategyMatched = incident->getStrategyMatched();
				}
			}
			dripResult_p = NULL;
		}
		return sensitive;
	}

	// 常规策略与零星策略合并接口
	BOOL merge_update_strategy(const char *reg_strategy, const char* drip_strategy)
	{
		BOOL ret = pILDFcr->mergeUpdateStrategy(reg_strategy, drip_strategy);
		if (!ret) {
			//printf("merge update failed");
		}
		return ret;
	}

	//验证策略能否获取未下载规则文件列表
	BOOL getRuleFilesList(std::string v_strFileMd5, std::string v_strFileGuid, int v_iFtpSvrId)
	{
		char chRet[MAX_FILE_COUNT] = { 0 };
		BOOL bRet = pILDFcr->getRuleFileList(m_strategy, NULL, chRet, MAX_FILE_COUNT);

		rapidjson::Json_Document doc;
		if (doc.Parse<0>(chRet).HasParseError())
		{
			return false;
		}

		if (doc.HasMember("ruleFiles") && doc["ruleFiles"].IsArray())
		{
			rapidjson::Json_Value & str_array = doc["ruleFiles"];
			// 遍历整个数组查找匹配的文件，而不是只检查第一个
			for (rapidjson::SizeType i = 0; i < str_array.Size(); i++)
			{
				rapidjson::Json_Value & str = str_array[i];
				if ((str.HasMember("fileMd5") && str["fileMd5"].IsString()))
				{
					if (!v_strFileMd5.compare(str["fileMd5"].GetString()))
					{
						if (!v_strFileGuid.compare(str["fileGuid"].GetString()))
						{
							if (v_iFtpSvrId == str["ftpSvrId"].GetInt())
							{
								return true;
							}
						}
					}
				}
			}
		}
		return false;
	}

	//验证策略能否获取未下载规则文件列表（服务端模式）
	BOOL getRuleFilesListForServer(std::string v_strFileMd5, std::string v_strFileGuid, int v_iFtpSvrId)
	{
		char chRet[MAX_FILE_COUNT] = { 0 };
		BOOL bRet = pILDFcr->getRuleFileList(m_strategy, NULL, chRet, MAX_FILE_COUNT, TRUE);

		rapidjson::Json_Document doc;
		if (doc.Parse<0>(chRet).HasParseError())
		{
			printf("getRuleFilesListForServer: JSON parse error for MD5=%s\n", v_strFileMd5.c_str());
			return false;
		}

		if (doc.HasMember("ruleFiles") && doc["ruleFiles"].IsArray())
		{
			rapidjson::Json_Value & str_array = doc["ruleFiles"];
			// 遍历整个数组查找匹配的文件，而不是只检查第一个
			for (rapidjson::SizeType i = 0; i < str_array.Size(); i++)
			{
				rapidjson::Json_Value & str = str_array[i];
				if ((str.HasMember("fileMd5") && str["fileMd5"].IsString()))
				{
					if (!v_strFileMd5.compare(str["fileMd5"].GetString()))
					{
						if (!v_strFileGuid.compare(str["fileGuid"].GetString()))
						{
							if (v_iFtpSvrId == str["ftpSvrId"].GetInt())
							{
								printf("getRuleFilesListForServer: FOUND MD5=%s\n", v_strFileMd5.c_str());
								return true;
							}
						}
					}
				}
			}
		}
		printf("getRuleFilesListForServer: NOT FOUND MD5=%s\n", v_strFileMd5.c_str());
		return false;
	}

	// json 字段适配函数测试 -- 该测试用例当前都已注释 2025-07-08
	//BOOL detect_promise(const char *path, void **result_p, void **dripResult_p, int num, std::promise<BOOL> &&promise)
	//{
	//	if (NULL == pILDFcr)
	//	{
	//		return FALSE;
	//	}

	//	FCRPARAM fcrParam;
	//	fcrParam.use_all_rule = TRUE;   //启用所有规则
	//	fcrParam.target_class_code = 3; //达到或超过代码3，即停止
	//	fcrParam.output_detail = TRUE;  //要求返回时输出策略的细节信息
	//	fcrParam.opType = num;
	//	fcrParam.fcrInfo = "{ \"opType\": 0, \"IP\": \"***********\"}";

	//	if (result_p)
	//		*result_p = NULL;
	//	if (dripResult_p)
	//		*dripResult_p = NULL;
	//	BOOL sensitive = FALSE;

	//	sensitive = pILDFcr->executeFCR(path, &fcrParam, result_p, dripResult_p);

	//	promise.set_value(sensitive);
	//	return sensitive;
	//}

	// 检测文本截断
	BOOL update_strategy(const char *strategy)
	{
		if (!pILDFcr)
		{
			if (ldfcr_InitStartup())
			{
				ldfcr_CreateInstance((void **)&pILDFcr);
				if (NULL == pILDFcr)
				{
					return -1;
				}
			}
			else
			{
				return -1;
			}
		}

		BOOL ret = pILDFcr->updateStrategy(strategy);
		if (!ret) {
			printf("update failed");
			return -1;
		}
		return 0;
	}

	//===================2023 7 12服务端代码合并 高级算法advance
	//高级算法统一使用通用接口  后续有特殊情况在单独增加用例

	void finalize()
	{
		if (NULL != pILDFcr) {
			pILDFcr->Release();
			pILDFcr = NULL;
		}
	}
};

class LdfcrFixture : public QuickTest
{
protected:
	Ldfcr ldfcr;
};

#endif // TEST_CLASS_H
